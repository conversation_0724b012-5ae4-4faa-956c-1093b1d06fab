#!/bin/bash

# 最基本的Pod库列表工具

echo "=== 基本Pod库发现 ==="

# 直接列出所有Pod库
echo "1. 发现的Pod库:"

find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
    dir=$(dirname "$podspec")
    name=$(basename "$dir")
    
    # 跳过备份和应用目录
    if [[ "$name" == backup_* ]] || [[ "$name" == "BBJShellApp" ]] || [[ "$name" == "MeetYouApp" ]] || [[ "$name" == "YoubaobaoApp_ci2" ]] || [[ "$name" == "meetyou-ci-5" ]] || [[ "$name" == "iOS" ]] || [[ "$name" == "vibe_coding" ]] || [[ "$name" == "pod_clean" ]]; then
        echo "  跳过: $name"
        continue
    fi
    
    # 统计资源
    size=$(du -sh "$dir" 2>/dev/null | cut -f1)
    png_count=$(find "$dir" -name "*.png" 2>/dev/null | wc -l)
    jpg_count=$(find "$dir" -name "*.jpg" -o -name "*.jpeg" 2>/dev/null | wc -l)
    
    echo "  ✅ $name ($size) - PNG: $png_count, JPG: $jpg_count"
done

echo ""
echo "2. 选择要优化的Pod库:"
echo "请输入Pod库名称，或输入 'all' 优化所有Pod库"
echo -n "选择: "
read selection

if [ "$selection" = "all" ]; then
    echo "将优化所有适合的Pod库..."
    
    find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
        dir=$(dirname "$podspec")
        name=$(basename "$dir")
        
        # 跳过备份和应用目录
        if [[ "$name" == backup_* ]] || [[ "$name" == "BBJShellApp" ]] || [[ "$name" == "MeetYouApp" ]] || [[ "$name" == "YoubaobaoApp_ci2" ]] || [[ "$name" == "meetyou-ci-5" ]] || [[ "$name" == "iOS" ]] || [[ "$name" == "vibe_coding" ]] || [[ "$name" == "pod_clean" ]]; then
            continue
        fi
        
        # 检查是否有图片资源
        png_count=$(find "$dir" -name "*.png" 2>/dev/null | wc -l)
        jpg_count=$(find "$dir" -name "*.jpg" -o -name "*.jpeg" 2>/dev/null | wc -l)
        
        if [ $((png_count + jpg_count)) -gt 0 ]; then
            echo ""
            echo "=== 优化 $name ==="
            
            # 创建备份目录
            backup_dir="backup_${name}_$(date +%Y%m%d_%H%M%S)"
            mkdir -p "$backup_dir"
            
            # 进入Pod目录
            cd "$dir"
            
            # 压缩大PNG文件
            echo "压缩PNG文件..."
            find . -name "*.png" -size +50k | head -5 | while read file; do
                if [ -f "$file" ]; then
                    original_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                    
                    # 备份
                    cp "$file" "../pod_clean/$backup_dir/"
                    
                    # 压缩（如果有工具）
                    if command -v pngquant >/dev/null 2>&1; then
                        pngquant --quality=65-80 --force --ext .png "$file" 2>/dev/null || true
                        new_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                        saved=$((original_size - new_size))
                        if [ $saved -gt 0 ]; then
                            echo "  压缩: $(basename "$file") 节省 $((saved / 1024))KB"
                        fi
                    else
                        echo "  跳过: $(basename "$file") (需要安装pngquant)"
                    fi
                fi
            done
            
            # 返回
            cd - > /dev/null
            
            echo "  备份: $backup_dir"
        else
            echo "跳过 $name (无图片资源)"
        fi
    done
    
    echo ""
    echo "批量优化完成！"
    
elif [ -n "$selection" ]; then
    echo "将优化Pod库: $selection"
    
    # 查找指定的Pod库
    target_dir=""
    find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
        dir=$(dirname "$podspec")
        name=$(basename "$dir")
        
        if [ "$name" = "$selection" ]; then
            echo "找到Pod库: $dir"
            # 这里可以添加单个Pod库的优化逻辑
            break
        fi
    done
    
else
    echo "未选择任何Pod库"
fi
