#!/bin/bash

# 调试Pod库发现功能

echo "=== 调试Pod库发现 ==="

# 查找Pod库
pod_libs=()
excluded_dirs=("BBJShellApp" "MeetYouApp" "YoubaobaoApp_ci2" "meetyou-ci-5" "iOS" "vibe_coding" "ImageComparison" "ImageQualityComparison" "PictureComparison" "pod_clean")

echo "1. 查找所有.podspec文件:"
find .. -maxdepth 2 -name "*.podspec" | while read podspec_file; do
    echo "  发现: $podspec_file"
    
    dir=$(dirname "$podspec_file")
    dir_name=$(basename "$dir")
    
    echo "    目录: $dir"
    echo "    目录名: $dir_name"
    
    # 跳过备份目录
    if [[ "$dir_name" == backup_* ]] || [[ "$dir_name" == resource_backup_* ]] || [[ "$dir_name" == unused_resources_backup_* ]]; then
        echo "    状态: 跳过备份目录"
        continue
    fi
    
    # 检查是否在排除列表中
    excluded=false
    for excluded_dir in "${excluded_dirs[@]}"; do
        if [[ "$dir_name" == "$excluded_dir" ]]; then
            excluded=true
            break
        fi
    done
    
    if [ "$excluded" = true ]; then
        echo "    状态: 跳过排除目录"
    elif [ "$dir" = ".." ]; then
        echo "    状态: 跳过上级目录"
    else
        echo "    状态: ✅ 有效Pod库"
        pod_libs+=("$dir")
        
        # 分析资源
        png_count=$(find "$dir" -name "*.png" 2>/dev/null | wc -l)
        jpg_count=$(find "$dir" -name "*.jpg" -o -name "*.jpeg" 2>/dev/null | wc -l)
        size=$(du -sh "$dir" 2>/dev/null | cut -f1)
        
        echo "    大小: $size"
        echo "    PNG: $png_count 个"
        echo "    JPG: $jpg_count 个"
    fi
    echo ""
done

echo "总计发现Pod库数量: ${#pod_libs[@]}"
