#!/bin/bash

# ZZIMYMain 智能资源清理脚本
# 作者: AI Assistant  
# 日期: 2025-01-14
# 用途: 智能检测和清理未使用的资源文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 智能资源引用检测
check_resource_usage() {
    local file="$1"
    local filename=$(basename "$file" | sed 's/\.[^.]*$//')
    
    # 移除常见的分辨率后缀
    local base_name=$(echo "$filename" | sed 's/@[0-9]x$//')
    
    # 移除常见的状态后缀
    local clean_name=$(echo "$base_name" | sed 's/_normal$//' | sed 's/_selected$//' | sed 's/_hover$//' | sed 's/_pressed$//' | sed 's/_disabled$//' | sed 's/_highlighted$//')
    
    # 搜索模式数组
    local search_patterns=(
        "$filename"           # 完整文件名
        "$base_name"          # 去除分辨率后缀
        "$clean_name"         # 去除状态后缀
        "${clean_name}_"      # 基础名称+下划线（可能有其他后缀）
    )
    
    local total_matches=0
    local match_details=""
    
    # 在多种文件类型中搜索
    local file_types="--include=*.m --include=*.h --include=*.mm --include=*.swift --include=*.xib --include=*.storyboard --include=*.plist --include=*.json"
    
    for pattern in "${search_patterns[@]}"; do
        local matches=$(grep -r "$pattern" $file_types zzimymain/ 2>/dev/null | wc -l)
        if [ "$matches" -gt 0 ]; then
            total_matches=$((total_matches + matches))
            match_details="$match_details$pattern($matches) "
        fi
    done
    
    echo "$total_matches:$match_details"
}

# 测试特定资源文件
test_resource() {
    local test_file="$1"
    
    if [ ! -f "$test_file" ]; then
        log_error "文件不存在: $test_file"
        return 1
    fi
    
    log_info "测试资源文件: $(basename "$test_file")"
    
    local usage_result=$(check_resource_usage "$test_file")
    local match_count=$(echo "$usage_result" | cut -d':' -f1)
    local match_details=$(echo "$usage_result" | cut -d':' -f2)
    
    echo "匹配次数: $match_count"
    echo "匹配详情: $match_details"
    
    if [ "$match_count" -eq 0 ]; then
        log_warning "该文件可能未被使用，可以安全删除"
    elif [ "$match_count" -lt 3 ]; then
        log_warning "该文件引用次数较少，建议手动检查"
    else
        log_success "该文件有足够的引用，应该保留"
    fi
}

# 智能检测和清理未使用的资源
clean_unused_resources() {
    log_info "开始智能检测未使用的资源..."
    
    local auto_delete=${1:-false}
    local unused_count=0
    local deleted_count=0
    local fuzzy_match_count=0
    local total_saved=0
    
    # 创建临时文件存储结果
    local candidates_file=$(mktemp)
    local fuzzy_file=$(mktemp)
    
    # 创建备份目录
    local backup_dir="resource_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    log_info "扫描资源文件..."

    # 获取所有资源文件
    local all_files=$(find zzimymain -name "*.png" -o -name "*.jpg" -o -name "*.gif")
    local total_files=$(echo "$all_files" | wc -l)
    local processed=0

    echo "$all_files" | while read file; do
        processed=$((processed + 1))

        # 显示进度
        if [ $((processed % 50)) -eq 0 ]; then
            log_info "进度: $processed/$total_files"
        fi

        # 使用智能检测函数
        local usage_result=$(check_resource_usage "$file")
        local match_count=$(echo "$usage_result" | cut -d':' -f1)
        local match_details=$(echo "$usage_result" | cut -d':' -f2)

        if [ "$match_count" -eq 0 ]; then
            # 完全没有匹配，记录为删除候选
            local file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
            echo "$file:$file_size" >> "$candidates_file"
        elif [ "$match_count" -lt 3 ]; then
            # 引用次数很少，可能是误匹配，需要提示
            echo "$file -> $match_details" >> "$fuzzy_file"
            fuzzy_match_count=$((fuzzy_match_count + 1))
        fi

        unused_count=$((unused_count + 1))
    done

    log_info "扫描完成，共处理 $total_files 个文件"
    
    # 处理删除候选文件
    if [ -s "$candidates_file" ]; then
        local candidate_count=$(wc -l < "$candidates_file")
        local candidate_size=$(awk -F: '{sum += $2} END {print sum}' "$candidates_file")
        
        log_info "发现 $candidate_count 个完全未使用的资源，可节省 $((candidate_size / 1024))KB"
        
        echo "未使用的资源文件列表:"
        while IFS=: read -r file_path file_size; do
            echo "  $(basename "$file_path") ($(((file_size / 1024)))KB)"
        done < "$candidates_file"
        
        if [ "$auto_delete" = "true" ]; then
            # 自动删除模式
            while IFS=: read -r file_path file_size; do
                # 备份文件
                cp "$file_path" "$backup_dir/"
                rm "$file_path"
                log_success "删除: $(basename "$file_path") (节省 $((file_size / 1024))KB)"
                deleted_count=$((deleted_count + 1))
                total_saved=$((total_saved + file_size))
            done < "$candidates_file"
        else
            # 手动确认模式
            echo -n "是否删除这些文件? [y/n]: "
            read -r response
            case "$response" in
                [Yy])
                    while IFS=: read -r file_path file_size; do
                        # 备份文件
                        cp "$file_path" "$backup_dir/"
                        rm "$file_path"
                        log_success "删除: $(basename "$file_path") (节省 $((file_size / 1024))KB)"
                        deleted_count=$((deleted_count + 1))
                        total_saved=$((total_saved + file_size))
                    done < "$candidates_file"
                    ;;
                *)
                    log_info "跳过删除"
                    ;;
            esac
        fi
    else
        log_success "未发现完全未使用的资源"
    fi
    
    # 处理低频引用资源
    if [ -s "$fuzzy_file" ]; then
        log_warning "发现 $fuzzy_match_count 个低频引用资源，建议手动检查:"
        cat "$fuzzy_file" | while read line; do
            log_warning "  $line"
        done
    fi
    
    # 输出统计结果
    if [ $deleted_count -gt 0 ]; then
        log_success "删除 $deleted_count 个未使用资源，节省 $((total_saved / 1024))KB"
        log_info "备份目录: $backup_dir"
    else
        rmdir "$backup_dir" 2>/dev/null || true
    fi
    
    # 清理临时文件
    rm -f "$candidates_file" "$fuzzy_file"
}

# 显示帮助信息
show_help() {
    echo "ZZIMYMain 智能资源清理脚本"
    echo ""
    echo "用法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -t, --test FILE     测试特定资源文件的使用情况"
    echo "  -c, --clean         交互式清理未使用资源"
    echo "  -a, --auto          自动清理未使用资源（无需确认）"
    echo ""
    echo "示例:"
    echo "  $0 -t zzimymain/Seeyou/Bundles/theme_night_ios.bundle/<EMAIL>"
    echo "  $0 -c"
    echo "  $0 -a"
}

# 主函数
main() {
    # 检查是否在正确的目录
    if [ ! -d "zzimymain" ]; then
        log_error "未找到zzimymain目录，请在正确的目录下运行此脚本"
        exit 1
    fi
    
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        -t|--test)
            if [ -z "$2" ]; then
                log_error "请指定要测试的文件路径"
                exit 1
            fi
            test_resource "$2"
            ;;
        -c|--clean)
            clean_unused_resources false
            ;;
        -a|--auto)
            clean_unused_resources true
            ;;
        "")
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
