#!/bin/bash

# ZZIMYMain Pod库包体积优化脚本
# 作者: AI Assistant
# 日期: 2025-01-14
# 用途: 自动化执行包体积优化任务

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要工具
check_tools() {
    log_info "检查必要工具..."
    
    local tools=("pngquant" "jpegoptim" "md5sum")
    local missing_tools=()
    
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "缺少必要工具: ${missing_tools[*]}"
        log_info "请安装缺少的工具:"
        log_info "brew install pngquant jpegoptim coreutils"
        exit 1
    fi
    
    log_success "所有必要工具已安装"
}

# 备份原始文件
backup_files() {
    log_info "创建备份..."
    
    local backup_dir="backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份podspec文件
    cp zzimymain/ZZIMYMain.podspec "$backup_dir/"
    
    # 备份大图片文件
    find zzimymain -size +100k -name "*.png" -o -name "*.jpg" | while read file; do
        local rel_path="${file#zzimymain/}"
        local backup_path="$backup_dir/$rel_path"
        mkdir -p "$(dirname "$backup_path")"
        cp "$file" "$backup_path"
    done
    
    log_success "备份完成: $backup_dir"
    echo "$backup_dir" > .backup_path
}

# 修复podspec重复依赖
fix_podspec() {
    log_info "修复podspec重复依赖..."
    
    local podspec="zzimymain/ZZIMYMain.podspec"
    
    # 检查是否存在重复的IMYTools依赖
    local imy_tools_count=$(grep -c "s.dependency 'IMYTools'" "$podspec" || true)
    
    if [ "$imy_tools_count" -gt 1 ]; then
        log_warning "发现重复的IMYTools依赖 ($imy_tools_count 次)"
        
        # 创建临时文件
        local temp_file=$(mktemp)
        
        # 移除重复的IMYTools依赖（保留第一个）
        awk '
        /s\.dependency .IMYTools./ {
            if (!seen) {
                print
                seen = 1
            }
            next
        }
        { print }
        ' "$podspec" > "$temp_file"
        
        mv "$temp_file" "$podspec"
        log_success "已修复重复依赖"
    else
        log_info "未发现重复依赖"
    fi
}

# 压缩图片
compress_images() {
    log_info "开始压缩图片..."
    
    local total_saved=0
    local processed_count=0
    
    # 压缩PNG文件（大于50KB的）
    find zzimymain -name "*.png" -size +50k | while read file; do
        local original_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
        
        # 使用pngquant压缩
        if pngquant --quality=65-80 --ext .png --force "$file" 2>/dev/null; then
            local new_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
            local saved=$((original_size - new_size))
            
            if [ $saved -gt 0 ]; then
                log_success "压缩 $(basename "$file"): 节省 $((saved / 1024))KB"
                total_saved=$((total_saved + saved))
            fi
        else
            log_warning "无法压缩 $(basename "$file")"
        fi
        
        processed_count=$((processed_count + 1))
    done
    
    # 压缩JPEG文件（大于50KB的）
    find zzimymain -name "*.jpg" -size +50k | while read file; do
        local original_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
        
        # 使用jpegoptim压缩
        if jpegoptim --max=80 --strip-all "$file" 2>/dev/null; then
            local new_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
            local saved=$((original_size - new_size))
            
            if [ $saved -gt 0 ]; then
                log_success "压缩 $(basename "$file"): 节省 $((saved / 1024))KB"
                total_saved=$((total_saved + saved))
            fi
        else
            log_warning "无法压缩 $(basename "$file")"
        fi
        
        processed_count=$((processed_count + 1))
    done
    
    log_success "图片压缩完成，共处理 $processed_count 个文件，节省 $((total_saved / 1024))KB"
}

# 查找重复资源
find_duplicate_resources() {
    log_info "查找重复资源..."
    
    local temp_file=$(mktemp)
    
    # 计算所有图片文件的MD5
    find zzimymain -name "*.png" -o -name "*.jpg" -o -name "*.gif" | while read file; do
        local md5=$(md5sum "$file" | cut -d' ' -f1)
        echo "$md5 $file"
    done | sort > "$temp_file"
    
    # 查找重复的MD5
    local duplicates=$(cut -d' ' -f1 "$temp_file" | uniq -d)
    
    if [ -n "$duplicates" ]; then
        log_warning "发现重复资源:"
        echo "$duplicates" | while read md5; do
            log_warning "MD5: $md5"
            grep "^$md5 " "$temp_file" | cut -d' ' -f2- | sed 's/^/  /'
        done
    else
        log_success "未发现重复资源"
    fi
    
    rm "$temp_file"
}

# 智能资源引用检测
check_resource_usage() {
    local file="$1"
    local filename=$(basename "$file" | sed 's/\.[^.]*$//')

    # 移除常见的分辨率后缀
    local base_name=$(echo "$filename" | sed 's/@[0-9]x$//')

    # 移除常见的状态后缀
    local clean_name=$(echo "$base_name" | sed 's/_normal$//' | sed 's/_selected$//' | sed 's/_hover$//' | sed 's/_pressed$//' | sed 's/_disabled$//' | sed 's/_highlighted$//')

    # 搜索模式数组
    local search_patterns=(
        "$filename"           # 完整文件名
        "$base_name"          # 去除分辨率后缀
        "$clean_name"         # 去除状态后缀
        "${clean_name}_"      # 基础名称+下划线（可能有其他后缀）
    )

    local total_matches=0
    local match_details=""

    # 在多种文件类型中搜索
    local file_types="--include=*.m --include=*.h --include=*.mm --include=*.swift --include=*.xib --include=*.storyboard --include=*.plist --include=*.json"

    for pattern in "${search_patterns[@]}"; do
        local matches=$(grep -r "$pattern" $file_types zzimymain/ 2>/dev/null | wc -l)
        if [ "$matches" -gt 0 ]; then
            total_matches=$((total_matches + matches))
            match_details="$match_details$pattern($matches) "
        fi
    done

    echo "$total_matches:$match_details"
}

# 智能检测和清理未使用的资源
find_unused_resources() {
    log_info "智能检测未使用的资源..."

    local unused_count=0
    local deleted_count=0
    local fuzzy_match_count=0
    local total_saved=0
    local auto_delete=${1:-false}  # 是否自动删除，默认false

    # 创建临时文件存储结果
    local unused_file=$(mktemp)
    local deleted_file=$(mktemp)
    local fuzzy_file=$(mktemp)
    local candidates_file=$(mktemp)

    find zzimymain -name "*.png" -o -name "*.jpg" -o -name "*.gif" | while read file; do
        # 使用智能检测函数
        local usage_result=$(check_resource_usage "$file")
        local match_count=$(echo "$usage_result" | cut -d':' -f1)
        local match_details=$(echo "$usage_result" | cut -d':' -f2)

        if [ "$match_count" -eq 0 ]; then
            # 完全没有匹配，记录为删除候选
            local file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
            echo "$file:$file_size" >> "$candidates_file"
            log_warning "发现未使用资源: $(basename "$file") (可节省 $((file_size / 1024))KB)"
        elif [ "$match_count" -lt 3 ]; then
            # 引用次数很少，可能是误匹配，需要提示
            log_warning "低频引用资源: $(basename "$file") (匹配: $match_details)"
            echo "$file -> $match_details" >> "$fuzzy_file"
            fuzzy_match_count=$((fuzzy_match_count + 1))
        else
            # 有足够的引用，保留
            continue
        fi

        unused_count=$((unused_count + 1))
    done

    # 处理删除候选文件
    if [ -s "$candidates_file" ]; then
        local candidate_count=$(wc -l < "$candidates_file")
        local candidate_size=$(awk -F: '{sum += $2} END {print sum}' "$candidates_file")

        log_info "发现 $candidate_count 个完全未使用的资源，可节省 $((candidate_size / 1024))KB"

        if [ "$auto_delete" = "true" ]; then
            # 自动删除模式
            while IFS=: read -r file_path file_size; do
                rm "$file_path"
                log_success "删除: $(basename "$file_path") (节省 $((file_size / 1024))KB)"
                deleted_count=$((deleted_count + 1))
                total_saved=$((total_saved + file_size))
            done < "$candidates_file"
        else
            # 手动确认模式
            log_warning "以下文件将被删除 (输入 'y' 确认删除，'n' 跳过，'a' 全部删除):"
            while IFS=: read -r file_path file_size; do
                echo "  $(basename "$file_path") ($(((file_size / 1024)))KB)"
            done < "$candidates_file"

            echo -n "是否删除这些文件? [y/n/a]: "
            read -r response
            case "$response" in
                [Yy]|[Aa])
                    while IFS=: read -r file_path file_size; do
                        rm "$file_path"
                        log_success "删除: $(basename "$file_path") (节省 $((file_size / 1024))KB)"
                        deleted_count=$((deleted_count + 1))
                        total_saved=$((total_saved + file_size))
                    done < "$candidates_file"
                    ;;
                *)
                    log_info "跳过删除，文件列表已保存到临时文件"
                    ;;
            esac
        fi
    fi

    # 输出统计结果
    if [ $deleted_count -gt 0 ]; then
        log_success "删除 $deleted_count 个未使用资源，节省 $((total_saved / 1024))KB"
    fi

    if [ $fuzzy_match_count -gt 0 ]; then
        log_warning "发现 $fuzzy_match_count 个低频引用资源，建议手动检查:"
        if [ -f "$fuzzy_file" ]; then
            cat "$fuzzy_file" | while read line; do
                log_warning "  $line"
            done
        fi
    fi

    if [ $unused_count -eq 0 ]; then
        log_success "未发现可清理的未使用资源"
    else
        log_info "资源检测完成: 删除 $deleted_count 个，低频引用 $fuzzy_match_count 个"
    fi

    # 清理临时文件
    rm -f "$unused_file" "$deleted_file" "$fuzzy_file" "$candidates_file"
}

# 生成优化报告
generate_report() {
    log_info "生成优化报告..."
    
    local report_file="optimization_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "ZZIMYMain 包体积优化报告"
        echo "========================="
        echo "生成时间: $(date)"
        echo ""
        
        echo "优化前后对比:"
        echo "-------------"
        if [ -f .backup_path ]; then
            local backup_dir=$(cat .backup_path)
            echo "备份目录: $backup_dir"
        fi
        
        echo ""
        echo "当前大小:"
        du -sh zzimymain/
        
        echo ""
        echo "模块大小分布:"
        du -sh zzimymain/Seeyou zzimymain/Rights zzimymain/Widget zzimymain/Watch
        
        echo ""
        echo "资源文件统计:"
        echo "PNG文件数: $(find zzimymain -name "*.png" | wc -l)"
        echo "JPG文件数: $(find zzimymain -name "*.jpg" | wc -l)"
        echo "音频文件数: $(find zzimymain -name "*.m4a" | wc -l)"
        
        echo ""
        echo "大文件Top10:"
        find zzimymain -type f -exec ls -lh {} \; | sort -k5 -hr | head -10
        
        echo ""
        echo "代码统计:"
        echo "源文件数: $(find zzimymain -name "*.m" -o -name "*.h" | wc -l)"
        echo "代码行数: $(find zzimymain -name "*.m" -o -name "*.h" | xargs wc -l | tail -1)"
        
    } > "$report_file"
    
    log_success "优化报告已生成: $report_file"
}

# 主函数
main() {
    log_info "开始ZZIMYMain包体积优化..."
    
    # 检查是否在正确的目录
    if [ ! -d "zzimymain" ]; then
        log_error "未找到zzimymain目录，请在正确的目录下运行此脚本"
        exit 1
    fi
    
    # 执行优化步骤
    check_tools
    backup_files
    fix_podspec
    compress_images
    find_duplicate_resources
    find_unused_resources false  # false表示需要用户确认，true表示自动删除
    generate_report
    
    log_success "优化完成！"
    log_info "请查看生成的报告文件了解详细信息"
    log_info "如需恢复，请使用备份目录中的文件"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
