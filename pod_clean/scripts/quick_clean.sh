#!/bin/bash

# 快速清理确认未使用的资源文件
# 基于之前的智能检测结果

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}[INFO]${NC} 开始清理确认未使用的资源文件..."

# 创建备份目录
backup_dir="unused_resources_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$backup_dir"
echo -e "${BLUE}[INFO]${NC} 备份目录: $backup_dir"

# 已确认未使用的文件列表（基于智能检测结果）
unused_files=(
    "zzimymain/Seeyou/Bundles/theme_night_ios.bundle/<EMAIL>"
    "zzimymain/Seeyou/Bundles/theme_night_ios.bundle/<EMAIL>"
    "zzimymain/Seeyou/Bundles/theme_night_ios.bundle/<EMAIL>"
    "zzimymain/Seeyou/Bundles/theme_night_ios.bundle/<EMAIL>"
    "zzimymain/Seeyou/Bundles/theme_night_ios.bundle/<EMAIL>"
)

total_saved=0
deleted_count=0

echo -e "${YELLOW}[WARNING]${NC} 将要删除以下文件:"
for file in "${unused_files[@]}"; do
    if [ -f "$file" ]; then
        file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
        echo "  $(basename "$file") ($(((file_size / 1024)))KB)"
    fi
done

echo -n "确认删除这些文件? [y/N]: "
read -r response

if [[ "$response" =~ ^[Yy]$ ]]; then
    for file in "${unused_files[@]}"; do
        if [ -f "$file" ]; then
            # 备份文件
            cp "$file" "$backup_dir/"
            
            # 获取文件大小
            file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
            
            # 删除文件
            rm "$file"
            
            echo -e "${GREEN}[SUCCESS]${NC} 删除: $(basename "$file") (节省 $((file_size / 1024))KB)"
            deleted_count=$((deleted_count + 1))
            total_saved=$((total_saved + file_size))
        else
            echo -e "${YELLOW}[WARNING]${NC} 文件不存在: $file"
        fi
    done
    
    echo ""
    echo -e "${GREEN}[SUCCESS]${NC} 清理完成!"
    echo "总计删除 $deleted_count 个文件，节省 $((total_saved / 1024))KB"
    echo "备份位置: $backup_dir"
else
    echo -e "${BLUE}[INFO]${NC} 取消删除操作"
    rmdir "$backup_dir"
fi
