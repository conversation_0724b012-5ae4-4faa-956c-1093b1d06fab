#!/bin/bash

# 测试Pod库发现功能

echo "=== Pod库发现测试 ==="

# 查找所有.podspec文件（在上级目录）
echo "1. 查找所有.podspec文件:"
find .. -name "*.podspec" -maxdepth 2 | sort

echo ""
echo "2. 分析Pod库结构:"

# 排除的目录列表
excluded_dirs=("BBJShellApp" "MeetYouApp" "YoubaobaoApp_ci2" "meetyou-ci-5" "iOS" "vibe_coding" "ImageComparison" "ImageQualityComparison" "PictureComparison")

# 查找Pod库
pod_count=0
while IFS= read -r podspec_file; do
    if [ -f "$podspec_file" ]; then
        dir=$(dirname "$podspec_file")
        dir_name=$(basename "$dir")
        
        # 检查是否在排除列表中
        excluded=false
        for excluded_dir in "${excluded_dirs[@]}"; do
            if [[ "$dir_name" == "$excluded_dir" ]]; then
                excluded=true
                break
            fi
        done
        
        if [ "$excluded" = false ] && [ "$dir" != ".." ]; then
            pod_count=$((pod_count + 1))
            size=$(du -sh "$dir" 2>/dev/null | cut -f1)
            
            # 统计资源文件
            png_count=$(find "$dir" -name "*.png" 2>/dev/null | wc -l)
            jpg_count=$(find "$dir" -name "*.jpg" -o -name "*.jpeg" 2>/dev/null | wc -l)
            
            echo "  $pod_count. $dir_name ($size)"
            echo "     - Podspec: $podspec_file"
            echo "     - PNG文件: $png_count 个"
            echo "     - JPG文件: $jpg_count 个"
            
            if [ $((png_count + jpg_count)) -gt 0 ]; then
                echo "     - 状态: ✅ 适合优化"
            else
                echo "     - 状态: ⚠️ 无图片资源"
            fi
            echo ""
        else
            echo "  跳过: $dir_name (应用项目或工具目录)"
        fi
    fi
done < <(find .. -name "*.podspec" -maxdepth 2)

echo "总计发现 $pod_count 个Pod库"
