#!/bin/bash

# 增强版Pod库批量优化工具
# 处理更多图片文件，提供更详细的统计

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 全局统计变量
TOTAL_PNG_PROCESSED=0
TOTAL_JPG_PROCESSED=0
TOTAL_SPACE_SAVED=0
TOTAL_PODS_OPTIMIZED=0

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[HEADER]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                增强版Pod库批量优化工具 v3.0                   ║"
    echo "║                                                              ║"
    echo "║  功能: 全面优化Pod库中的所有图片资源                          ║"
    echo "║  支持: PNG/JPG压缩、详细统计、进度显示                        ║"
    echo "║  安全: 自动备份、Git忽略、完整日志                            ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 检查依赖工具
check_dependencies() {
    local missing_tools=()
    
    if ! command -v pngquant >/dev/null 2>&1; then
        missing_tools+=("pngquant")
    fi
    
    if ! command -v jpegoptim >/dev/null 2>&1; then
        missing_tools+=("jpegoptim")
    fi
    
    if [ ${#missing_tools[@]} -gt 0 ]; then
        log_warning "缺少以下优化工具："
        for tool in "${missing_tools[@]}"; do
            echo "  - $tool"
        done
        echo ""
        echo "安装方法："
        echo "  macOS: brew install pngquant jpegoptim"
        echo "  Ubuntu: sudo apt-get install pngquant jpegoptim"
        echo ""
        echo -n "是否继续（部分功能可能不可用）? [y/N]: "
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        log_success "所有依赖工具已安装"
    fi
}

# 发现Pod库
discover_pods() {
    log_info "扫描Pod库..."

    local excluded=("BBJShellApp" "MeetYouApp" "YoubaobaoApp_ci2" "meetyou-ci-5" "iOS" "vibe_coding" "ImageComparison" "ImageQualityComparison" "PictureComparison" "pod_clean")

    local temp_file=$(mktemp)

    find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
        local dir=$(dirname "$podspec")
        local name=$(basename "$dir")

        # 跳过备份目录
        if [[ "$name" == backup_* ]] || [[ "$name" == resource_backup_* ]] || [[ "$name" == unused_resources_backup_* ]]; then
            continue
        fi

        # 检查排除列表
        local skip=false
        for ex in "${excluded[@]}"; do
            if [[ "$name" == "$ex" ]]; then
                skip=true
                break
            fi
        done

        if [ "$skip" = false ]; then
            echo "$dir" >> "$temp_file"
        fi
    done

    # 直接输出临时文件内容
    cat "$temp_file"
    rm -f "$temp_file"
}

# 分析Pod库图片资源
analyze_pod_images() {
    local pod_dir="$1"
    local pod_name=$(basename "$pod_dir")
    
    log_info "分析Pod库: $pod_name"
    
    # 统计所有图片
    local total_png=$(find "$pod_dir" -name "*.png" 2>/dev/null | wc -l)
    local total_jpg=$(find "$pod_dir" -name "*.jpg" -o -name "*.jpeg" 2>/dev/null | wc -l)
    local total_gif=$(find "$pod_dir" -name "*.gif" 2>/dev/null | wc -l)
    
    # 统计大文件
    local large_png=$(find "$pod_dir" -name "*.png" -size +50k 2>/dev/null | wc -l)
    local medium_png=$(find "$pod_dir" -name "*.png" -size +20k -size -50k 2>/dev/null | wc -l)
    local small_png=$(find "$pod_dir" -name "*.png" -size -20k 2>/dev/null | wc -l)
    
    local large_jpg=$(find "$pod_dir" -name "*.jpg" -o -name "*.jpeg" -size +50k 2>/dev/null | wc -l)
    
    # 计算总大小
    local png_size=$(find "$pod_dir" -name "*.png" -exec stat -f%z {} \; 2>/dev/null | awk '{sum += $1} END {print sum/1024/1024}')
    local jpg_size=$(find "$pod_dir" -name "*.jpg" -o -name "*.jpeg" -exec stat -f%z {} \; 2>/dev/null | awk '{sum += $1} END {print sum/1024/1024}')
    
    echo "  📊 图片统计:"
    echo "    PNG: $total_png 个 (大型: $large_png, 中型: $medium_png, 小型: $small_png) - ${png_size:-0}MB"
    echo "    JPG: $total_jpg 个 (大型: $large_jpg) - ${jpg_size:-0}MB"
    echo "    GIF: $total_gif 个"
    
    local total_images=$((total_png + total_jpg + total_gif))
    local optimizable=$((large_png + large_jpg))
    
    if [ $optimizable -gt 0 ]; then
        echo "  ✅ 可优化文件: $optimizable 个"
        return 0
    else
        echo "  ⚠️  无大文件需要优化"
        return 1
    fi
}

# 优化单个Pod库（增强版）
optimize_pod_enhanced() {
    local pod_dir="$1"
    local pod_name=$(basename "$pod_dir")
    
    log_header "开始优化: $pod_name"
    
    # 创建备份目录
    local backup_dir="backup_${pod_name}_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    local pod_png_saved=0
    local pod_jpg_saved=0
    local pod_png_count=0
    local pod_jpg_count=0
    
    # 进入Pod目录
    cd "$pod_dir"
    
    # 优化PNG文件（分级处理）
    log_info "优化PNG文件..."
    
    # 处理大型PNG文件 (>50KB)
    echo "  处理大型PNG文件 (>50KB)..."
    find . -name "*.png" -size +50k | while read file; do
        if [ -f "$file" ]; then
            local original_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
            
            # 备份原文件
            cp "$file" "../pod_clean/$backup_dir/"
            
            # 压缩
            if command -v pngquant >/dev/null 2>&1; then
                pngquant --quality=65-80 --force --ext .png "$file" 2>/dev/null || true
                
                local new_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                local saved=$((original_size - new_size))
                
                if [ $saved -gt 0 ]; then
                    echo "    压缩: $(basename "$file") 节省 $((saved / 1024))KB"
                    pod_png_saved=$((pod_png_saved + saved))
                fi
                pod_png_count=$((pod_png_count + 1))
            fi
        fi
    done
    
    # 处理中型PNG文件 (20KB-50KB)
    echo "  处理中型PNG文件 (20KB-50KB)..."
    find . -name "*.png" -size +20k -size -50k | while read file; do
        if [ -f "$file" ]; then
            local original_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
            
            # 备份原文件
            cp "$file" "../pod_clean/$backup_dir/"
            
            # 使用更温和的压缩
            if command -v pngquant >/dev/null 2>&1; then
                pngquant --quality=70-85 --force --ext .png "$file" 2>/dev/null || true
                
                local new_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                local saved=$((original_size - new_size))
                
                if [ $saved -gt 0 ]; then
                    echo "    压缩: $(basename "$file") 节省 $((saved / 1024))KB"
                    pod_png_saved=$((pod_png_saved + saved))
                fi
                pod_png_count=$((pod_png_count + 1))
            fi
        fi
    done
    
    # 优化JPG文件
    log_info "优化JPG文件..."
    find . -name "*.jpg" -o -name "*.jpeg" | while read file; do
        if [ -f "$file" ]; then
            local original_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
            
            # 备份原文件
            cp "$file" "../pod_clean/$backup_dir/"
            
            # 优化
            if command -v jpegoptim >/dev/null 2>&1; then
                jpegoptim --max=80 --strip-all "$file" 2>/dev/null || true
                
                local new_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                local saved=$((original_size - new_size))
                
                if [ $saved -gt 0 ]; then
                    echo "    优化: $(basename "$file") 节省 $((saved / 1024))KB"
                    pod_jpg_saved=$((pod_jpg_saved + saved))
                fi
                pod_jpg_count=$((pod_jpg_count + 1))
            fi
        fi
    done
    
    # 返回到pod_clean目录
    cd - > /dev/null
    
    # 更新全局统计
    TOTAL_PNG_PROCESSED=$((TOTAL_PNG_PROCESSED + pod_png_count))
    TOTAL_JPG_PROCESSED=$((TOTAL_JPG_PROCESSED + pod_jpg_count))
    TOTAL_SPACE_SAVED=$((TOTAL_SPACE_SAVED + pod_png_saved + pod_jpg_saved))
    TOTAL_PODS_OPTIMIZED=$((TOTAL_PODS_OPTIMIZED + 1))
    
    # 显示Pod库优化结果
    local total_pod_saved=$((pod_png_saved + pod_jpg_saved))
    if [ $total_pod_saved -gt 0 ]; then
        log_success "[$pod_name] 优化完成"
        echo "  PNG: $pod_png_count 个文件，节省 $((pod_png_saved / 1024))KB"
        echo "  JPG: $pod_jpg_count 个文件，节省 $((pod_jpg_saved / 1024))KB"
        echo "  总计: 节省 $((total_pod_saved / 1024))KB"
        echo "  备份: $backup_dir"
    else
        log_info "[$pod_name] 优化完成，无明显空间节省"
        echo "  备份: $backup_dir"
    fi
    echo ""
}

# 生成详细报告
generate_detailed_report() {
    local report_file="增强版优化报告_$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$report_file" << EOF
# 增强版Pod库批量优化报告

## 📊 优化总览

**优化时间**: $(date)  
**优化Pod库数量**: $TOTAL_PODS_OPTIMIZED  
**处理PNG文件**: $TOTAL_PNG_PROCESSED 个  
**处理JPG文件**: $TOTAL_JPG_PROCESSED 个  
**总计节省空间**: $((TOTAL_SPACE_SAVED / 1024))KB ≈ $((TOTAL_SPACE_SAVED / 1024 / 1024))MB  

## 🔧 优化策略

### PNG文件优化
- **大型文件 (>50KB)**: 质量65-80%压缩
- **中型文件 (20KB-50KB)**: 质量70-85%压缩
- **小型文件 (<20KB)**: 跳过处理

### JPG文件优化
- **所有JPG文件**: 质量80%优化，移除元数据

## 📁 备份目录

所有修改的文件都已备份到以下目录：
EOF

    # 添加备份目录列表
    ls -d backup_* 2>/dev/null | while read backup_dir; do
        echo "- $backup_dir" >> "$report_file"
    done
    
    cat >> "$report_file" << EOF

## 🛡️ 安全保障

- ✅ 所有修改文件已自动备份
- ✅ 支持完全回滚操作
- ✅ Git忽略文件已配置
- ✅ 详细操作日志记录

## 📈 后续建议

1. **功能测试**: 验证应用功能正常
2. **性能测试**: 检查启动时间改善
3. **深度清理**: 使用智能工具清理未使用资源
4. **持续监控**: 定期检查包体积变化

---
报告生成时间: $(date)
EOF

    log_success "详细报告已生成: $report_file"
}

# 主函数
main() {
    show_banner
    
    # 检查依赖
    check_dependencies
    
    # 发现Pod库
    local pods_str=$(discover_pods)
    local PODS=()
    while IFS= read -r line; do
        if [ -n "$line" ]; then
            PODS+=("$line")
        fi
    done <<< "$pods_str"
    
    if [ ${#PODS[@]} -eq 0 ]; then
        log_error "未发现任何Pod库"
        exit 1
    fi
    
    log_success "发现 ${#PODS[@]} 个Pod库"
    
    case "${1:-}" in
        --analyze|-a)
            log_header "Pod库图片资源分析"
            for pod in "${PODS[@]}"; do
                analyze_pod_images "$pod"
                echo ""
            done
            ;;
        --optimize|-o)
            log_header "开始增强版批量优化"
            
            # 分析可优化的Pod库
            local optimizable_pods=()
            for pod in "${PODS[@]}"; do
                if analyze_pod_images "$pod"; then
                    optimizable_pods+=("$pod")
                fi
                echo ""
            done
            
            if [ ${#optimizable_pods[@]} -eq 0 ]; then
                log_warning "没有适合优化的Pod库"
                exit 0
            fi
            
            echo "发现 ${#optimizable_pods[@]} 个适合优化的Pod库"
            echo -n "确认开始增强版优化? [y/N]: "
            read -r confirm
            
            if [[ "$confirm" =~ ^[Yy]$ ]]; then
                for pod in "${optimizable_pods[@]}"; do
                    optimize_pod_enhanced "$pod"
                done
                
                # 生成报告
                generate_detailed_report
                
                log_success "增强版批量优化完成！"
                echo "总计处理: PNG $TOTAL_PNG_PROCESSED 个, JPG $TOTAL_JPG_PROCESSED 个"
                echo "总计节省: $((TOTAL_SPACE_SAVED / 1024))KB ≈ $((TOTAL_SPACE_SAVED / 1024 / 1024))MB"
            else
                log_info "取消优化"
            fi
            ;;
        --help|-h|*)
            echo "增强版Pod库优化工具 v3.0"
            echo ""
            echo "用法:"
            echo "  $0 --analyze    分析所有Pod库的图片资源"
            echo "  $0 --optimize   执行增强版批量优化"
            echo "  $0 --help       显示帮助"
            echo ""
            echo "特性:"
            echo "  - 处理所有大中型PNG文件"
            echo "  - 优化所有JPG文件"
            echo "  - 详细的统计和报告"
            echo "  - 完整的备份和安全保障"
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
