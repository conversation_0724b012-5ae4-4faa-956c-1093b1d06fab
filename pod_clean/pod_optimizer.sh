#!/bin/bash

# Pod库优化工具 - 主入口脚本
# 版本: v5.0
# 功能: 统一入口，支持完整优化、增量优化、重置等功能

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_DIR="$SCRIPT_DIR/scripts"
DB_DIR="$SCRIPT_DIR/db"
DB_FILE="$DB_DIR/optimization.db"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    Pod库优化工具 v5.0                         ║"
    echo "║                                                              ║"
    echo "║  功能: 完整优化、增量优化、重置、智能清理                       ║"
    echo "║  特性: SQLite记录、Git重置、自动报告生成                       ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 初始化数据库
init_database() {
    mkdir -p "$DB_DIR"
    
    if [ ! -f "$DB_FILE" ]; then
        log_info "初始化优化记录数据库..."
        sqlite3 "$DB_FILE" << 'EOF'
CREATE TABLE IF NOT EXISTS optimizations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    pod_name TEXT NOT NULL,
    optimization_type TEXT NOT NULL,
    file_count INTEGER DEFAULT 0,
    space_saved INTEGER DEFAULT 0,
    optimization_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    backup_path TEXT,
    status TEXT DEFAULT 'completed'
);

CREATE TABLE IF NOT EXISTS optimization_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    optimization_id INTEGER,
    file_path TEXT NOT NULL,
    original_size INTEGER,
    optimized_size INTEGER,
    compression_ratio REAL,
    FOREIGN KEY (optimization_id) REFERENCES optimizations (id)
);

CREATE INDEX IF NOT EXISTS idx_pod_name ON optimizations(pod_name);
CREATE INDEX IF NOT EXISTS idx_optimization_time ON optimizations(optimization_time);
EOF
        log_success "数据库初始化完成"
    fi
}

# 检查Pod库是否已优化
is_pod_optimized() {
    local pod_name="$1"
    local count=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM optimizations WHERE pod_name='$pod_name' AND status='completed';")
    [ "$count" -gt 0 ]
}

# 记录优化结果
record_optimization() {
    local pod_name="$1"
    local opt_type="$2"
    local file_count="$3"
    local space_saved="$4"
    local backup_path="$5"
    
    sqlite3 "$DB_FILE" << EOF
INSERT INTO optimizations (pod_name, optimization_type, file_count, space_saved, backup_path)
VALUES ('$pod_name', '$opt_type', $file_count, $space_saved, '$backup_path');
EOF
}

# 显示优化历史
show_optimization_history() {
    log_info "优化历史记录:"
    echo ""
    
    if [ ! -f "$DB_FILE" ]; then
        log_warning "暂无优化记录"
        return
    fi
    
    sqlite3 -header -column "$DB_FILE" << 'EOF'
SELECT 
    pod_name as "Pod库",
    optimization_type as "优化类型",
    file_count as "文件数",
    ROUND(space_saved/1024.0, 2) as "节省(KB)",
    datetime(optimization_time, 'localtime') as "优化时间"
FROM optimizations 
ORDER BY optimization_time DESC 
LIMIT 20;
EOF
}

# Git贮藏功能
git_stash_optimization() {
    log_info "Git贮藏管理..."

    echo ""
    echo "贮藏选项:"
    echo "1. 贮藏所有优化修改"
    echo "2. 查看贮藏列表"
    echo "3. 恢复最近的贮藏"
    echo "4. 恢复指定的贮藏"
    echo "5. 删除指定的贮藏"
    echo "6. 查看当前修改状态"
    echo "7. 返回主菜单"
    echo -n "请选择: "
    read -r choice

    case "$choice" in
        1)
            # 检查是否有修改
            if ! git diff --quiet || ! git diff --cached --quiet; then
                log_info "贮藏当前的优化修改..."
                local stash_message="Pod优化修改_$(date +%Y%m%d_%H%M%S)"
                git stash push -m "$stash_message" -- "**/*.png" "**/*.jpg" "**/*.jpeg" 2>/dev/null || \
                git stash save "$stash_message" 2>/dev/null || true
                log_success "已贮藏优化修改: $stash_message"
            else
                log_warning "没有发现需要贮藏的修改"
            fi
            ;;
        2)
            log_info "贮藏列表:"
            git stash list | grep -E "(Pod优化|optimization)" || echo "没有找到优化相关的贮藏"
            ;;
        3)
            log_info "恢复最近的贮藏..."
            git stash pop 2>/dev/null && log_success "已恢复最近的贮藏" || log_error "恢复失败或没有贮藏"
            ;;
        4)
            echo "贮藏列表:"
            git stash list | head -10
            echo -n "请输入贮藏编号 (如: stash@{0}): "
            read -r stash_ref
            if [ -n "$stash_ref" ]; then
                git stash apply "$stash_ref" 2>/dev/null && log_success "已恢复贮藏: $stash_ref" || log_error "恢复失败"
            fi
            ;;
        5)
            echo "贮藏列表:"
            git stash list | head -10
            echo -n "请输入要删除的贮藏编号 (如: stash@{0}): "
            read -r stash_ref
            if [ -n "$stash_ref" ]; then
                echo -n "确认删除贮藏 $stash_ref? [y/N]: "
                read -r confirm
                if [[ "$confirm" =~ ^[Yy]$ ]]; then
                    git stash drop "$stash_ref" 2>/dev/null && log_success "已删除贮藏: $stash_ref" || log_error "删除失败"
                fi
            fi
            ;;
        6)
            log_info "当前修改状态:"
            git status --porcelain | grep -E '\.(png|jpg|jpeg)$' | head -20 || echo "没有图片文件修改"
            ;;
        7)
            return 0
            ;;
        *)
            log_error "无效选择"
            ;;
    esac
}

# 增量优化
incremental_optimization() {
    log_info "开始增量优化..."
    
    # 发现所有Pod库
    local pods=()
    local new_pods=()
    
    find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
        local dir=$(dirname "$podspec")
        local name=$(basename "$dir")
        
        # 跳过排除的目录
        if [[ "$name" == backup_* ]] || [[ "$name" == "BBJShellApp" ]] || [[ "$name" == "MeetYouApp" ]] || [[ "$name" == "YoubaobaoApp_ci2" ]] || [[ "$name" == "meetyou-ci-5" ]] || [[ "$name" == "iOS" ]] || [[ "$name" == "vibe_coding" ]] || [[ "$name" == "pod_clean" ]]; then
            continue
        fi
        
        echo "$name"
    done > /tmp/all_pods.txt
    
    local total_pods=$(wc -l < /tmp/all_pods.txt)
    local new_count=0
    
    echo "发现 $total_pods 个Pod库，检查优化状态..."
    echo ""
    
    while read -r pod_name; do
        if ! is_pod_optimized "$pod_name"; then
            echo "  ✅ $pod_name (未优化)"
            new_count=$((new_count + 1))
        else
            echo "  ⏭️  $pod_name (已优化)"
        fi
    done < /tmp/all_pods.txt
    
    rm -f /tmp/all_pods.txt
    
    if [ $new_count -eq 0 ]; then
        log_success "所有Pod库均已优化，无需增量优化"
        return 0
    fi
    
    echo ""
    log_info "发现 $new_count 个未优化的Pod库"
    echo -n "是否执行增量优化? [y/N]: "
    read -r confirm
    
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        log_info "调用完整优化脚本进行增量处理..."
        "$SCRIPTS_DIR/complete_pod_optimizer.sh" --incremental
    fi
}

# 显示帮助信息
show_help() {
    echo "Pod库优化工具 v5.0"
    echo ""
    echo "用法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -f, --full          执行完整优化"
    echo "  -i, --incremental   执行增量优化"
    echo "  -s, --stash         Git贮藏管理"
    echo "  -c, --clean         智能资源清理"
    echo "  -s, --status        显示优化状态"
    echo "  -H, --history       显示优化历史"
    echo ""
    echo "交互模式:"
    echo "  $0                  进入交互式菜单"
}

# 交互式菜单
interactive_menu() {
    while true; do
        echo ""
        echo "=== Pod库优化工具菜单 ==="
        echo "1. 完整优化 (处理所有Pod库)"
        echo "2. 增量优化 (仅处理未优化的Pod库)"
        echo "3. 智能资源清理"
        echo "4. Git贮藏管理"
        echo "5. 显示优化状态"
        echo "6. 显示优化历史"
        echo "7. 退出"
        echo -n "请选择: "
        read -r choice
        
        case "$choice" in
            1)
                log_info "启动完整优化..."
                "$SCRIPTS_DIR/complete_pod_optimizer.sh"
                ;;
            2)
                incremental_optimization
                ;;
            3)
                log_info "启动智能资源清理..."
                "$SCRIPTS_DIR/clean_unused_resources.sh" -c
                ;;
            4)
                git_stash_optimization
                ;;
            5)
                show_optimization_history
                ;;
            6)
                show_optimization_history
                ;;
            7)
                log_info "退出程序"
                break
                ;;
            *)
                log_error "无效选择，请重新输入"
                ;;
        esac
    done
}

# 主函数
main() {
    show_banner
    
    # 初始化数据库
    init_database
    
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        -f|--full)
            log_info "执行完整优化..."
            "$SCRIPTS_DIR/complete_pod_optimizer.sh"
            ;;
        -i|--incremental)
            incremental_optimization
            ;;
        -s|--stash)
            git_stash_optimization
            ;;
        -c|--clean)
            "$SCRIPTS_DIR/clean_unused_resources.sh" -c
            ;;
        -s|--status|-H|--history)
            show_optimization_history
            ;;
        "")
            interactive_menu
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
