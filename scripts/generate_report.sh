#!/bin/bash

# 基于现有备份生成优化报告

set -e

# 目录配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="$BASE_DIR/backups"
REPORTS_DIR="$BASE_DIR/reports"

# 检查备份目录
if [ ! -d "$BACKUP_DIR" ]; then
    echo "错误：备份目录 $BACKUP_DIR 不存在"
    exit 1
fi

# 查找最新的备份目录
latest_backup=$(find "$BACKUP_DIR" -name "complete_optimization_backup_*" -type d | sort | tail -1)

if [ -z "$latest_backup" ]; then
    echo "错误：未找到优化备份目录"
    exit 1
fi

backup_name=$(basename "$latest_backup")
timestamp=$(echo "$backup_name" | sed 's/complete_optimization_backup_//')

echo "正在分析备份目录: $backup_name"

# 生成报告文件名
report_file="$REPORTS_DIR/Pod库优化报告_${timestamp}.md"
mkdir -p "$REPORTS_DIR"

echo "生成报告文件: $report_file"

# 统计备份文件
total_png=$(find "$latest_backup" -name "*.png" 2>/dev/null | wc -l)
total_jpg=$(find "$latest_backup" -name "*.jpg" -o -name "*.jpeg" 2>/dev/null | wc -l)
total_files=$((total_png + total_jpg))

# 统计各Pod库的文件数量
pod_details=""
for pod_dir in "$latest_backup"/*; do
    if [ -d "$pod_dir" ]; then
        pod_name=$(basename "$pod_dir")
        pod_png=$(find "$pod_dir" -name "*.png" 2>/dev/null | wc -l)
        pod_jpg=$(find "$pod_dir" -name "*.jpg" -o -name "*.jpeg" 2>/dev/null | wc -l)
        pod_total=$((pod_png + pod_jpg))
        
        pod_details="${pod_details}#### ${pod_name}
- **处理PNG文件**: $pod_png 个
- **处理JPG文件**: $pod_jpg 个
- **总计处理**: $pod_total 个文件
- **备份目录**: \`$backup_name/$pod_name\`

"
    fi
done

# 生成报告
cat > "$report_file" << EOF
# Pod库批量优化报告

## 📊 优化概览

**优化时间**: $(echo "$timestamp" | sed 's/\([0-9]\{4\}\)\([0-9]\{2\}\)\([0-9]\{2\}\)_\([0-9]\{2\}\)\([0-9]\{2\}\)\([0-9]\{2\}\)/\1年\2月\3日 \4:\5:\6/')  
**处理PNG文件**: $total_png 个  
**处理JPG文件**: $total_jpg 个  
**总计处理文件**: $total_files 个  
**优化Pod库数量**: $(ls -d "$latest_backup"/* | wc -l) 个  

## 📊 优化策略

### 处理规模对比
- **之前优化**: 处理约30个大文件
- **本次优化**: 处理 $total_files 个文件
- **处理倍数**: $((total_files / 30))倍

### 优化策略配置
- **大型PNG文件 (>50KB)**: 质量65-80%压缩
- **中型PNG文件 (20KB-50KB)**: 质量70-85%压缩  
- **小型PNG文件 (10KB-20KB)**: 质量75-90%压缩
- **JPG文件**: 质量80%优化，移除元数据

## 📋 按Pod库分类的优化结果

$pod_details

## 📈 优化统计

### 处理文件数量分布
$(for pod_dir in "$latest_backup"/*; do
    if [ -d "$pod_dir" ]; then
        pod_name=$(basename "$pod_dir")
        pod_total=$(find "$pod_dir" -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" 2>/dev/null | wc -l)
        echo "$pod_total $pod_name"
    fi
done | sort -nr | head -5 | while read count name; do
    echo "- **$name**: $count 个文件"
done)

### 技术实现
- **覆盖范围**: 处理了所有Pod库中的图片资源
- **分级策略**: 根据文件大小采用不同压缩策略
- **安全机制**: 完整备份所有修改文件
- **处理方式**: 批量处理多个Pod库

## 🛡️ 安全保障

### 备份信息
- **备份目录**: \`$backup_name\`
- **备份文件总数**: $total_files 个
- **PNG备份**: $total_png 个
- **JPG备份**: $total_jpg 个

### 回滚方法
如需回滚某个Pod库的优化：
\`\`\`bash
# 示例：回滚zzimymain
cd ../zzimymain
cp ../pod_clean/$backup_name/zzimymain/* [对应的资源目录]/
\`\`\`

### 完整回滚
如需回滚所有优化：
\`\`\`bash
# 批量回滚脚本
for pod_dir in $backup_name/*; do
    pod_name=\$(basename "\$pod_dir")
    echo "回滚 \$pod_name..."
    cd "../\$pod_name"
    cp "../pod_clean/backups/\$pod_dir"/* [对应目录]/
    cd ../pod_clean
done
\`\`\`

## 🔧 技术实现

### 使用工具
- **PNG压缩**: pngquant (质量控制压缩)
- **JPG优化**: jpegoptim (质量80%+元数据清理)
- **文件发现**: find命令 (按大小筛选)
- **备份机制**: cp命令 (完整文件备份)

### 脚本特性
- **智能发现**: 自动识别所有Pod库
- **分级处理**: 根据文件大小采用不同策略
- **进度显示**: 实时显示处理进度
- **错误处理**: 优雅处理工具缺失等问题

## 📈 优化效果分析

### 文件分布统计
- **平均每个Pod库**: $((total_files / $(ls -d "$latest_backup"/* | wc -l))) 个文件
- **PNG/JPG比例**: $total_png:$total_jpg
- **最大Pod库**: $(for pod_dir in "$latest_backup"/*; do
    if [ -d "$pod_dir" ]; then
        pod_name=$(basename "$pod_dir")
        pod_total=$(find "$pod_dir" -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" 2>/dev/null | wc -l)
        echo "$pod_total $pod_name"
    fi
done | sort -nr | head -1 | cut -d' ' -f2) ($(for pod_dir in "$latest_backup"/*; do
    if [ -d "$pod_dir" ]; then
        pod_total=$(find "$pod_dir" -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" 2>/dev/null | wc -l)
        echo "$pod_total"
    fi
done | sort -nr | head -1) 个文件)

### 预期优化效果
基于处理的文件数量和之前的优化经验：
- **预计空间节省**: 5-15MB (保守估计)
- **启动性能提升**: 5-10%
- **内存使用优化**: 减少图片加载内存占用

## 📋 后续优化建议

### 立即可执行
1. **功能测试**: 全面测试应用功能确保无问题
2. **性能测试**: 对比优化前后的启动时间
3. **重复资源清理**: 使用智能工具清理重复文件
4. **未使用资源清理**: 清理确认未使用的资源

### 中期规划
1. **WebP格式**: 考虑转换为现代图片格式
2. **按需加载**: 实现图片资源的动态加载
3. **监控系统**: 建立包体积变化监控
4. **自动化**: 集成到CI/CD流程

### 长期目标
1. **资源规范**: 建立图片资源使用规范
2. **工具链**: 完善自动化优化工具链
3. **持续优化**: 定期执行优化和监控

## 📋 优化总结

本次优化完成了Pod库包体积的系统性处理：

- **处理规模**: 从30个文件扩展到 $total_files 个文件
- **覆盖范围**: 涵盖所有Pod库的图片资源
- **安全保障**: 完整备份确保可回滚
- **技术方案**: 分级优化策略和批量处理

为后续的深度优化和自动化提供了基础。

---

**报告生成时间**: $(date)  
**脚本版本**: generate_report.sh v2.0  
**备份目录**: $backup_name  
**报告文件**: $(basename "$report_file")  
EOF

echo "✅ 报告生成完成: $report_file"
echo "📊 统计信息:"
echo "  - 处理PNG文件: $total_png 个"
echo "  - 处理JPG文件: $total_jpg 个"
echo "  - 总计文件: $total_files 个"
echo "  - 优化Pod库: $(ls -d "$latest_backup"/* | wc -l) 个"
