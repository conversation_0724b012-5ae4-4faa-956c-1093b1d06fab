#!/bin/bash

# 完整Pod库优化工具 v5.0
# 功能: 处理所有Pod库中的所有图片文件
# 支持: 全面PNG/JPG优化、增量优化、SQLite记录

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 全局统计
TOTAL_PNG_PROCESSED=0
TOTAL_JPG_PROCESSED=0
TOTAL_SPACE_SAVED=0
OPTIMIZATION_START_TIME=$(date +%Y%m%d_%H%M%S)
OPTIMIZATION_DETAILS=""
PODS_OPTIMIZED=""
INCREMENTAL_MODE=false

# 目录配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"
DB_DIR="$BASE_DIR/db"
DB_FILE="$DB_DIR/optimization.db"
BACKUP_DIR="$BASE_DIR/backups"
REPORTS_DIR="$BASE_DIR/reports"

# 数据库操作函数
is_pod_optimized() {
    local pod_name="$1"
    if [ ! -f "$DB_FILE" ]; then
        return 1
    fi
    local count=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM optimizations WHERE pod_name='$pod_name' AND status='completed';" 2>/dev/null || echo "0")
    [ "$count" -gt 0 ]
}

record_optimization() {
    local pod_name="$1"
    local file_count="$2"
    local space_saved="$3"
    local backup_path="$4"
    
    if [ -f "$DB_FILE" ]; then
        sqlite3 "$DB_FILE" << EOF
INSERT INTO optimizations (pod_name, optimization_type, file_count, space_saved, backup_path)
VALUES ('$pod_name', 'complete', $file_count, $space_saved, '$backup_path');
EOF
    fi
}

echo -e "${CYAN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                完整Pod库优化工具 v5.0                         ║"
echo "║                                                              ║"
echo "║  功能: 处理所有Pod库中的所有图片文件                          ║"
echo "║  支持: 全面PNG/JPG优化、增量优化、SQLite记录                   ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# 检查依赖工具
echo -e "${BLUE}[INFO]${NC} 检查依赖工具..."
missing_tools=()
if ! command -v pngquant >/dev/null 2>&1; then
    missing_tools+=("pngquant")
fi
if ! command -v jpegoptim >/dev/null 2>&1; then
    missing_tools+=("jpegoptim")
fi

if [ ${#missing_tools[@]} -gt 0 ]; then
    echo -e "${YELLOW}[WARNING]${NC} 缺少工具: ${missing_tools[*]}"
    echo "安装方法: brew install pngquant jpegoptim"
    echo -n "是否继续? [y/N]: "
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        exit 1
    fi
else
    echo -e "${GREEN}[SUCCESS]${NC} 所有依赖工具已安装"
fi

# 检查命令行参数
if [ "$1" = "--incremental" ]; then
    INCREMENTAL_MODE=true
    echo "增量优化模式已启用"
fi

echo ""
echo "=== 完整Pod库发现和分析 ==="

# 发现Pod库
total_pods=0
optimizable_pods=0

find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
    dir=$(dirname "$podspec")
    name=$(basename "$dir")
    
    # 跳过排除的目录
    if [[ "$name" == backup_* ]] || [[ "$name" == "BBJShellApp" ]] || [[ "$name" == "MeetYouApp" ]] || [[ "$name" == "YoubaobaoApp_ci2" ]] || [[ "$name" == "meetyou-ci-5" ]] || [[ "$name" == "iOS" ]] || [[ "$name" == "vibe_coding" ]] || [[ "$name" == "pod_clean" ]]; then
        echo "  跳过: $name"
        continue
    fi
    
    # 计算目录大小
    size=$(du -sh "$dir" 2>/dev/null | cut -f1)
    
    # 统计图片文件
    cd "$dir"
    total_png=$(find . -name "*.png" | wc -l)
    total_jpg=$(find . -name "*.jpg" -o -name "*.jpeg" | wc -l)
    
    # 按大小分类PNG文件
    large_png=$(find . -name "*.png" -size +50k | wc -l)
    medium_png=$(find . -name "*.png" -size +20k -size -50k | wc -l)
    small_png=$(find . -name "*.png" -size +10k -size -20k | wc -l)
    
    optimizable=$((large_png + medium_png + small_png + total_jpg))
    
    echo "  ✅ $name ($size)"
    echo "     PNG: $(printf "%8d" $total_png) 个 (大型: $(printf "%8d" $large_png), 中型: $(printf "%8d" $medium_png), 小型: $(printf "%8d" $small_png))"
    echo "     JPG: $(printf "%8d" $total_jpg) 个"
    echo "     可优化: $optimizable 个文件"
    
    if [ $optimizable -gt 0 ]; then
        echo "     状态: 🎯 适合优化"
        optimizable_pods=$((optimizable_pods + 1))
    else
        echo "     状态: ⚠️ 无需优化"
    fi
    echo ""
    
    total_pods=$((total_pods + 1))
done

echo "总计: $total_pods 个Pod库，其中 $optimizable_pods 个适合优化"
echo ""

echo "2. 选择优化模式:"
echo "   a) 完整优化所有适合的Pod库"
echo "   b) 选择特定Pod库优化"
echo "   c) 仅分析，不执行优化"
echo -n "请选择 [a/b/c]: "
read selection

case "$selection" in
    a|A)
        echo ""
        if [ "$INCREMENTAL_MODE" = "true" ]; then
            echo "=== 开始增量优化 ==="
        else
            echo "=== 开始完整批量优化 ==="
        fi
        
        # 创建全局备份目录
        global_backup="$BACKUP_DIR/complete_optimization_backup_$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$global_backup"
        echo "全局备份目录: $global_backup"
        echo ""
        
        find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
            dir=$(dirname "$podspec")
            name=$(basename "$dir")
            
            # 跳过排除的目录
            if [[ "$name" == backup_* ]] || [[ "$name" == "BBJShellApp" ]] || [[ "$name" == "MeetYouApp" ]] || [[ "$name" == "YoubaobaoApp_ci2" ]] || [[ "$name" == "meetyou-ci-5" ]] || [[ "$name" == "iOS" ]] || [[ "$name" == "vibe_coding" ]] || [[ "$name" == "pod_clean" ]]; then
                continue
            fi
            
            cd "$dir"
            
            # 检查是否有可优化的文件
            large_png=$(find . -name "*.png" -size +50k | wc -l)
            medium_png=$(find . -name "*.png" -size +20k -size -50k | wc -l)
            small_png=$(find . -name "*.png" -size +10k -size -20k | wc -l)
            total_jpg=$(find . -name "*.jpg" -o -name "*.jpeg" | wc -l)
            optimizable=$((large_png + medium_png + small_png + total_jpg))
            
            if [ $optimizable -gt 0 ]; then
                # 增量模式下检查是否已优化
                if [ "$INCREMENTAL_MODE" = "true" ] && is_pod_optimized "$name"; then
                    echo "跳过 $name (已优化)"
                    continue
                fi
                
                echo "=== 优化 $name ==="
                
                # 创建Pod库备份目录
                pod_backup="$global_backup/$name"
                mkdir -p "$pod_backup"
                
                # 统计变量
                pod_png_count=0
                pod_jpg_count=0
                pod_png_saved=0
                pod_jpg_saved=0
                
                # 优化大型PNG文件 (>50KB)
                if [ $large_png -gt 0 ]; then
                    echo "  处理大型PNG文件 ($(printf "%6d" $large_png) 个)..."
                    find . -name "*.png" -size +50k | while read file; do
                        if [ -f "$file" ]; then
                            original_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                            
                            # 备份
                            cp "$file" "$pod_backup/"
                            
                            # 压缩
                            if command -v pngquant >/dev/null 2>&1; then
                                pngquant --quality=65-80 --force --ext .png "$file" 2>/dev/null || true
                                new_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                                saved=$((original_size - new_size))
                                if [ $saved -gt 0 ]; then
                                    echo "    压缩: $(basename "$file") 节省 $((saved / 1024))KB"
                                fi
                            fi
                        fi
                    done
                    # 统计实际处理的文件数
                    pod_png_count=$((pod_png_count + $(find . -name "*.png" -size +50k | wc -l)))
                fi
                
                # 优化中型PNG文件 (20KB-50KB)
                if [ $medium_png -gt 0 ]; then
                    echo "  处理中型PNG文件 ($(printf "%6d" $medium_png) 个)..."
                    find . -name "*.png" -size +20k -size -50k | while read file; do
                        if [ -f "$file" ]; then
                            original_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                            
                            # 备份
                            cp "$file" "$pod_backup/"
                            
                            # 温和压缩
                            if command -v pngquant >/dev/null 2>&1; then
                                pngquant --quality=70-85 --force --ext .png "$file" 2>/dev/null || true
                                new_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                                saved=$((original_size - new_size))
                                if [ $saved -gt 0 ]; then
                                    echo "    压缩: $(basename "$file") 节省 $((saved / 1024))KB"
                                fi
                            fi
                        fi
                    done
                    # 统计实际处理的文件数
                    pod_png_count=$((pod_png_count + $(find . -name "*.png" -size +20k -size -50k | wc -l)))
                fi
                
                # 优化小型PNG文件 (10KB-20KB)
                if [ $small_png -gt 0 ]; then
                    echo "  处理小型PNG文件 ($(printf "%6d" $small_png) 个)..."
                    find . -name "*.png" -size +10k -size -20k | while read file; do
                        if [ -f "$file" ]; then
                            original_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                            
                            # 备份
                            cp "$file" "$pod_backup/"
                            
                            # 轻度压缩
                            if command -v pngquant >/dev/null 2>&1; then
                                pngquant --quality=75-90 --force --ext .png "$file" 2>/dev/null || true
                                new_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                                saved=$((original_size - new_size))
                                if [ $saved -gt 0 ]; then
                                    echo "    压缩: $(basename "$file") 节省 $((saved / 1024))KB"
                                fi
                            fi
                        fi
                    done
                    # 统计实际处理的文件数
                    pod_png_count=$((pod_png_count + $(find . -name "*.png" -size +10k -size -20k | wc -l)))
                fi
                
                # 优化JPG文件
                if [ $total_jpg -gt 0 ]; then
                    echo "  处理JPG文件 ($(printf "%6d" $total_jpg) 个)..."
                    find . -name "*.jpg" -o -name "*.jpeg" | while read file; do
                        if [ -f "$file" ]; then
                            original_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                            
                            # 备份
                            cp "$file" "$pod_backup/"
                            
                            # 优化
                            if command -v jpegoptim >/dev/null 2>&1; then
                                jpegoptim --max=80 --strip-all "$file" 2>/dev/null || true
                                new_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                                saved=$((original_size - new_size))
                                if [ $saved -gt 0 ]; then
                                    echo "    优化: $(basename "$file") 节省 $((saved / 1024))KB"
                                fi
                            fi
                        fi
                    done
                    # 统计实际处理的文件数
                    pod_jpg_count=$((pod_jpg_count + $(find . -name "*.jpg" -o -name "*.jpeg" | wc -l)))
                fi
                
                # 显示结果
                total_pod_saved=$((pod_png_saved + pod_jpg_saved))
                echo "  结果: PNG $pod_png_count 个, JPG $pod_jpg_count 个, 节省 $((total_pod_saved / 1024))KB"
                echo "  备份: $pod_backup"
                echo ""
                
                # 记录优化详情
                OPTIMIZATION_DETAILS="${OPTIMIZATION_DETAILS}#### ${name}
- **处理文件**: PNG $pod_png_count 个, JPG $pod_jpg_count 个
- **节省空间**: $((total_pod_saved / 1024))KB
- **备份目录**: \`$pod_backup\`

"
                PODS_OPTIMIZED="${PODS_OPTIMIZED} ${name}"
                
                # 记录到数据库
                record_optimization "$name" "$((pod_png_count + pod_jpg_count))" "$total_pod_saved" "$pod_backup"
                
                # 更新全局统计
                TOTAL_PNG_PROCESSED=$((TOTAL_PNG_PROCESSED + pod_png_count))
                TOTAL_JPG_PROCESSED=$((TOTAL_JPG_PROCESSED + pod_jpg_count))
                TOTAL_SPACE_SAVED=$((TOTAL_SPACE_SAVED + total_pod_saved))
            fi
        done
        
        # 生成详细优化报告
        generate_optimization_report() {
            local report_file="$REPORTS_DIR/Pod库优化报告_${OPTIMIZATION_START_TIME}.md"
            local end_time=$(date +%Y%m%d_%H%M%S)
            local backup_files_png=$(find "$global_backup" -name "*.png" 2>/dev/null | wc -l)
            local backup_files_jpg=$(find "$global_backup" -name "*.jpg" -o -name "*.jpeg" 2>/dev/null | wc -l)
            local pods_count=$(echo "$PODS_OPTIMIZED" | wc -w)
            
            mkdir -p "$REPORTS_DIR"
            
            cat > "$report_file" << EOF
# Pod库批量优化报告

## 📊 优化概览

**优化开始时间**: $(date -j -f "%Y%m%d_%H%M%S" "$OPTIMIZATION_START_TIME" "+%Y年%m月%d日 %H:%M:%S" 2>/dev/null || echo "$OPTIMIZATION_START_TIME")  
**优化结束时间**: $(date -j -f "%Y%m%d_%H%M%S" "$end_time" "+%Y年%m月%d日 %H:%M:%S" 2>/dev/null || echo "$end_time")  
**处理PNG文件**: $backup_files_png 个  
**处理JPG文件**: $backup_files_jpg 个  
**总计处理文件**: $((backup_files_png + backup_files_jpg)) 个  
**优化Pod库数量**: $pods_count 个  
**总计节省空间**: $((TOTAL_SPACE_SAVED / 1024))KB ≈ $((TOTAL_SPACE_SAVED / 1024 / 1024))MB  

## 📊 按Pod库分类的优化结果

$OPTIMIZATION_DETAILS

## 🛡️ 安全保障

### 备份信息
- **备份目录**: \`$global_backup\`
- **备份文件总数**: $((backup_files_png + backup_files_jpg)) 个
- **PNG备份**: $backup_files_png 个
- **JPG备份**: $backup_files_jpg 个

### 回滚方法
如需回滚某个Pod库的优化：
\`\`\`bash
# 示例：回滚zzimymain
cd ../zzimymain
cp $global_backup/zzimymain/* [对应的资源目录]/
\`\`\`

## 🔧 技术细节

### 优化策略
- **大型PNG文件 (>50KB)**: 质量65-80%压缩
- **中型PNG文件 (20KB-50KB)**: 质量70-85%压缩  
- **小型PNG文件 (10KB-20KB)**: 质量75-90%压缩
- **JPG文件**: 质量80%优化，移除元数据

### 使用工具
- **PNG压缩**: pngquant
- **JPG优化**: jpegoptim
- **文件筛选**: find + stat
- **备份机制**: cp命令

## 📈 优化效果分析

### 文件分布
- **处理的Pod库**: $pods_count 个
- **平均每个Pod库处理文件**: $(($((backup_files_png + backup_files_jpg)) / pods_count)) 个
- **PNG/JPG比例**: $backup_files_png:$backup_files_jpg

---

**报告生成时间**: $(date)  
**脚本版本**: complete_pod_optimizer.sh v5.0  
**执行目录**: $(pwd)  
EOF

            echo "📄 详细优化报告已生成: $report_file"
        }

        echo ""
        echo "=== 完整优化总结 ==="
        echo "处理PNG文件: $TOTAL_PNG_PROCESSED 个"
        echo "处理JPG文件: $TOTAL_JPG_PROCESSED 个"
        echo "总计节省: $((TOTAL_SPACE_SAVED / 1024))KB ≈ $((TOTAL_SPACE_SAVED / 1024 / 1024))MB"
        echo "全局备份: $global_backup"

        # 生成详细报告
        generate_optimization_report

        echo ""
        echo "🎉 完整优化完成！"
        ;;
    *)
        echo "退出程序"
        exit 0
        ;;
esac
